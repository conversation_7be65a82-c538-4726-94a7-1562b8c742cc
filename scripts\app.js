document.addEventListener('DOMContentLoaded', () => {
    // ===== STATE MANAGEMENT =====
    const state = {
        loadedFiles: new Map(), // Map of file paths to File objects and metadata
        virtualFiles: new Map(), // Map for new files not yet saved
        folders: new Set(), // Set of folder paths
        folderExpansion: new Map(), // Track which folders are expanded
        currentFile: null,
        currentFilePath: null,
        editorContent: '',
        isDirty: false,
        settings: { 
            darkMode: localStorage.getItem('htmlPreviewerDarkMode') === 'true',
            lastFolder: localStorage.getItem('htmlPreviewerLastFolder'),
            panelStates: JSON.parse(localStorage.getItem('htmlPreviewerPanelStates') || '{"files": false, "code": false, "preview": false}'),
            panelSizes: JSON.parse(localStorage.getItem('htmlPreviewerPanelSizes') || '{"filesWidth": 300, "codeWidth": null}')
        },
        modifiedFiles: new Set(), // Track which files have unsaved changes
    };

    // ===== DOM ELEMENTS =====
    const dom = {
        app: document.getElementById('app'),
        htmlInput: document.getElementById('html-input'),
        syntaxHighlight: document.getElementById('syntax-highlight'),
        codeElement: document.querySelector('#syntax-highlight code'),
        previewIframe: document.getElementById('preview-output'),
        fileTree: document.getElementById('file-tree'),
        fileStatus: document.getElementById('file-status'),
        fileStatusIndicator: document.getElementById('file-status-indicator'),
        rootPath: document.getElementById('root-path'),
        consoleOutputContainer: document.getElementById('console-output-container'),
        consoleOutput: document.getElementById('console-output'),
        editorContainer: document.querySelector('.editor-container'),
        filesPane: document.getElementById('files-pane'),
        codePane: document.getElementById('code-pane'),
        previewPane: document.getElementById('preview-pane'),
        folderInput: document.getElementById('folder-input'),
        importInput: document.getElementById('import-input'),
        selectFolderBtn: document.getElementById('select-folder-btn'),
        saveAllBtn: document.getElementById('save-all-btn'),
        contextMenu: document.getElementById('context-menu'),
        newFileModal: document.getElementById('new-file-modal'),
        newFolderModal: document.getElementById('new-folder-modal'),
    };

    // ===== UTILITY FUNCTIONS =====
    function debounce(fn, wait) {
        let t;
        return (...args) => { clearTimeout(t); t = setTimeout(() => fn.apply(this, args), wait); };
    }

    function isBasicHtml(str) {
        return /<html[\s>]/i.test(str) && /<\/html>/i.test(str);
    }

    function showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        const bgColor = type === 'success' ? 'bg-green-500' : type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        toast.className = `toast px-4 py-2 rounded text-white shadow-lg ${bgColor}`;
        toast.textContent = message;
        // ARIA live region for accessibility
        toast.setAttribute('role', 'status');
        toast.setAttribute('aria-live', 'polite');
        container.appendChild(toast);
        setTimeout(() => toast.remove(), 4000);
    }

    function markDirty(dirty, filePath = state.currentFilePath) {
        if (filePath) {
            if (dirty) {
                state.modifiedFiles.add(filePath);
            } else {
                state.modifiedFiles.delete(filePath);
            }
        }
        
        state.isDirty = dirty;
        updateFileStatus();
        updateSaveAllButton();
    }

    function updateFileStatus() {
        const fileName = state.currentFile?.name || state.currentFilePath?.split('/').pop() || 'untitled.html';
        dom.fileStatus.textContent = fileName;
        
        // Update status indicator
        if (state.isDirty) {
            dom.fileStatusIndicator.className = 'status-indicator modified';
        } else {
            dom.fileStatusIndicator.className = 'status-indicator saved';
        }
    }

    function updateSaveAllButton() {
        const hasModified = state.modifiedFiles.size > 0;
        dom.saveAllBtn.disabled = !hasModified;
        dom.saveAllBtn.textContent = hasModified ? `Save All (${state.modifiedFiles.size})` : 'Save All';
    }

    function saveSettings() {
        localStorage.setItem('htmlPreviewerDarkMode', state.settings.darkMode);
        localStorage.setItem('htmlPreviewerLastFolder', state.settings.lastFolder || '');
        localStorage.setItem('htmlPreviewerPanelStates', JSON.stringify(state.settings.panelStates));
        localStorage.setItem('htmlPreviewerPanelSizes', JSON.stringify(state.settings.panelSizes || {}));
    }

    function generateUniqueFileName(baseName = 'untitled', extension = '.html') {
        let counter = 1;
        let fileName = `${baseName}${extension}`;

        while (state.loadedFiles.has(fileName) || state.virtualFiles.has(fileName)) {
            fileName = `${baseName}-${counter}${extension}`;
            counter++;
        }

        return fileName;
    }

    // ===== HTML VALIDATOR =====
    function validateHTML(html) {
        const errors = [];
        const warnings = [];

        try {
            // Basic HTML structure validation
            if (!html.trim()) {
                errors.push({ line: 1, column: 1, message: 'Document is empty', type: 'error' });
                return { errors, warnings };
            }

            // Check for DOCTYPE
            if (!html.match(/<!DOCTYPE\s+html>/i)) {
                warnings.push({ line: 1, column: 1, message: 'Missing DOCTYPE declaration', type: 'warning' });
            }

            // Check for basic HTML structure
            if (!html.match(/<html[^>]*>/i)) {
                errors.push({ line: 1, column: 1, message: 'Missing <html> tag', type: 'error' });
            }

            if (!html.match(/<head[^>]*>/i)) {
                warnings.push({ line: 1, column: 1, message: 'Missing <head> tag', type: 'warning' });
            }

            if (!html.match(/<body[^>]*>/i)) {
                warnings.push({ line: 1, column: 1, message: 'Missing <body> tag', type: 'warning' });
            }

            // Check for unclosed tags
            const tagPattern = /<(\/?)([\w-]+)(?:\s[^>]*)?>/g;
            const stack = [];
            const selfClosingTags = ['area', 'base', 'br', 'col', 'embed', 'hr', 'img', 'input', 'link', 'meta', 'param', 'source', 'track', 'wbr'];
            let match;
            let lineNumber = 1;

            while ((match = tagPattern.exec(html)) !== null) {
                const isClosing = match[1] === '/';
                const tagName = match[2].toLowerCase();
                const tagPosition = match.index;

                // Calculate line number
                const beforeTag = html.substring(0, tagPosition);
                lineNumber = (beforeTag.match(/\n/g) || []).length + 1;

                if (isClosing) {
                    // Closing tag
                    if (stack.length === 0) {
                        errors.push({
                            line: lineNumber,
                            column: tagPosition - beforeTag.lastIndexOf('\n'),
                            message: `Unexpected closing tag </${tagName}>`,
                            type: 'error'
                        });
                    } else {
                        const lastOpened = stack.pop();
                        if (lastOpened.name !== tagName) {
                            errors.push({
                                line: lineNumber,
                                column: tagPosition - beforeTag.lastIndexOf('\n'),
                                message: `Mismatched closing tag </${tagName}>, expected </${lastOpened.name}>`,
                                type: 'error'
                            });
                        }
                    }
                } else if (!selfClosingTags.includes(tagName) && !match[0].endsWith('/>')) {
                    // Opening tag (not self-closing)
                    stack.push({ name: tagName, line: lineNumber });
                }
            }

            // Check for unclosed tags
            while (stack.length > 0) {
                const unclosed = stack.pop();
                errors.push({
                    line: unclosed.line,
                    column: 1,
                    message: `Unclosed tag <${unclosed.name}>`,
                    type: 'error'
                });
            }

            // Check for common issues
            if (html.match(/<img[^>]*(?!alt=)[^>]*>/gi)) {
                const imgMatches = html.matchAll(/<img[^>]*>/gi);
                for (const imgMatch of imgMatches) {
                    if (!imgMatch[0].includes('alt=')) {
                        const beforeImg = html.substring(0, imgMatch.index);
                        const imgLine = (beforeImg.match(/\n/g) || []).length + 1;
                        warnings.push({
                            line: imgLine,
                            column: 1,
                            message: 'Image missing alt attribute for accessibility',
                            type: 'warning'
                        });
                    }
                }
            }

        } catch (error) {
            errors.push({ line: 1, column: 1, message: `Validation error: ${error.message}`, type: 'error' });
        }

        return { errors, warnings };
    }

    // ===== HTML FORMATTER =====
    function formatHTML(html) {
        try {
            // Remove extra whitespace and normalize
            let formatted = html.trim();

            // Basic HTML formatting with proper indentation
            let indent = 0;
            const indentSize = 2;
            const lines = [];

            // Split by tags while preserving content
            const tokens = formatted.split(/(<[^>]*>)/g).filter(token => token.length > 0);

            for (let i = 0; i < tokens.length; i++) {
                const token = tokens[i].trim();
                if (!token) continue;

                if (token.startsWith('<')) {
                    // It's a tag
                    if (token.startsWith('</')) {
                        // Closing tag - decrease indent before adding
                        indent = Math.max(0, indent - indentSize);
                        lines.push(' '.repeat(indent) + token);
                    } else if (token.endsWith('/>')) {
                        // Self-closing tag
                        lines.push(' '.repeat(indent) + token);
                    } else if (token.startsWith('<!')) {
                        // Comment or doctype
                        lines.push(' '.repeat(indent) + token);
                    } else {
                        // Opening tag
                        lines.push(' '.repeat(indent) + token);
                        // Don't increase indent for inline tags
                        const tagName = token.match(/<(\w+)/)?.[1]?.toLowerCase();
                        const inlineTags = ['a', 'span', 'strong', 'em', 'b', 'i', 'u', 'small', 'code', 'kbd', 'var', 'samp'];
                        if (tagName && !inlineTags.includes(tagName)) {
                            indent += indentSize;
                        }
                    }
                } else {
                    // Text content
                    if (token.length > 0) {
                        lines.push(' '.repeat(indent) + token);
                    }
                }
            }

            return lines.join('\n');
        } catch (error) {
            console.warn('HTML formatting failed:', error);
            return html; // Return original if formatting fails
        }
    }

    // ===== PERSISTENCE MANAGER =====
    class PersistenceManager {
        static save() {
            try {
                const data = {
                    virtualFiles: Array.from(state.virtualFiles.entries()),
                    folders: Array.from(state.folders),
                    folderExpansion: Array.from(state.folderExpansion.entries()),
                    currentFile: state.currentFilePath,
                    editorContent: state.editorContent,
                    modifiedFiles: Array.from(state.modifiedFiles),
                    timestamp: Date.now()
                };
                localStorage.setItem('htmlPreviewerSession', JSON.stringify(data));
            } catch (e) {
                console.warn('Failed to save session:', e);
            }
        }

        static restore() {
            try {
                const data = localStorage.getItem('htmlPreviewerSession');
                if (!data) return false;

                const session = JSON.parse(data);
                
                // Restore virtual files and folder states
                state.virtualFiles = new Map(session.virtualFiles || []);
                state.folders = new Set(session.folders || []);
                state.folderExpansion = new Map(session.folderExpansion || []);
                state.modifiedFiles = new Set(session.modifiedFiles || []);
                
                // Restore UI
                fileManager.renderFileTree();
                
                // Restore current file if it exists
                if (session.currentFile && (state.virtualFiles.has(session.currentFile) || state.loadedFiles.has(session.currentFile))) {
                    if (session.editorContent) {
                        editor.setContent(session.editorContent, state.modifiedFiles.has(session.currentFile));
                        state.currentFilePath = session.currentFile;
                        updateFileStatus();
                    }
                }
                
                return true;
            } catch (e) {
                console.warn('Failed to restore session:', e);
                return false;
            }
        }
    }

    // ===== Error Handling Utilities =====
    const ErrorCodes = {
        INVALID_NAME: 'E_INVALID_NAME',
        DUPLICATE_PATH: 'E_DUPLICATE_PATH',
        NOT_FOUND: 'E_NOT_FOUND',
        READ_FAILED: 'E_READ_FAILED',
        ZIP_NOT_LOADED: 'E_ZIP_NOT_LOADED',
        NO_MODIFIED: 'E_NO_MODIFIED',
        INVALID_HTML: 'E_INVALID_HTML',
        ABORTED: 'E_ABORTED',
        UNKNOWN: 'E_UNKNOWN'
    };

    function formatError(err, fallbackMessage = 'An unexpected error occurred') {
        if (!err) return fallbackMessage;
        if (typeof err === 'string') return err;
        if (err.message) return err.message;
        return fallbackMessage;
    }

    function safeAction(actionFn, { onError, finallyFn } = {}) {
        try {
            const res = actionFn();
            if (res && typeof res.then === 'function') {
                return res.catch(err => {
                    onError?.(err);
                    throw err;
                }).finally(() => {
                    finallyFn?.();
                });
            }
            return res;
        } catch (err) {
            onError?.(err);
            throw err;
        } finally {
            if (! (actionFn && typeof actionFn.then === 'function')) {
                finallyFn?.();
            }
        }
    }

    function reportError(context, err, code = ErrorCodes.UNKNOWN) {
        console.warn(`[${context}]`, code, err);
        showToast(`${context}: ${formatError(err)}`, 'error');
    }

    // ===== FILE MANAGER =====
    class FileManager {
        constructor() {
            this.setupEventListeners();
        }

        setupEventListeners() {
            dom.selectFolderBtn.addEventListener('click', () => dom.folderInput.click());
            dom.folderInput.addEventListener('change', (e) => this.handleFolderSelection(e.target.files));
            
            // New file/folder buttons
            document.getElementById('new-file-btn').addEventListener('click', () => this.showNewFileModal());
            document.getElementById('new-folder-btn').addEventListener('click', () => this.showNewFolderModal());
            document.getElementById('import-file-btn').addEventListener('click', () => dom.importInput.click());
            dom.importInput.addEventListener('change', (e) => this.handleFileImport(e.target.files));
            
            // Modal handlers
            this.setupModalHandlers();
            
            // Context menu
            this.setupContextMenu();
        }

        handleFolderSelection(files) {
            if (!files || !files.length) {
                showToast('No folder selected.', 'info');
                return;
            }

            safeAction(() => {
                state.loadedFiles.clear();
                state.folders.clear();

                Array.from(files).forEach(file => {
                    const path = file.webkitRelativePath;
                    if (!path) return;

                    const lastSlash = path.lastIndexOf('/');
                    const directory = lastSlash > -1 ? path.substring(0, lastSlash) : '';

                    if (directory) {
                        state.folders.add(directory);
                    }

                    if (file.name.toLowerCase().match(/\.(html|htm)$/)) {
                        state.loadedFiles.set(path, {
                            file: file,
                            isLoaded: false,
                            content: null
                        });
                    }
                });

                const firstFilePath = files[0].webkitRelativePath || files[0].name;
                const folderName = (firstFilePath?.split('/')?.[0]) || 'Project';
                state.settings.lastFolder = folderName;
                dom.rootPath.textContent = `Project: ${folderName}`;

                this.renderFileTree();
                saveSettings();
                PersistenceManager.save();

                showToast(`Loaded project: ${folderName}`, 'success');
            }, {
                onError: (err) => reportError('Load folder failed', err, ErrorCodes.READ_FAILED)
            });
        }

        handleFileImport(files) {
            if (!files || !files.length) {
                showToast('No file selected for import.', 'info');
                return;
            }

            const file = files[0];
            if (!file.name.toLowerCase().match(/\.(html|htm)$/)) {
                showToast('Only HTML files are supported for import.', 'error');
                return;
            }

            const fileName = generateUniqueFileName(file.name.replace(/\.(html|htm)$/, ''), '.html');

            safeAction(async () => {
                const content = await file.text();
                if (!content) {
                    throw new Error('File is empty or unreadable.');
                }

                state.virtualFiles.set(fileName, {
                    content: content,
                    isVirtual: true,
                    created: Date.now()
                });

                state.currentFile = null;
                state.currentFilePath = fileName;
                editor.setContent(content, true);
                this.renderFileTree();
                PersistenceManager.save();

                showToast(`Imported ${fileName}`, 'success');
            }, {
                onError: (err) => reportError('Import file failed', err, ErrorCodes.READ_FAILED)
            });
        }

        showNewFileModal() {
            dom.newFileModal.classList.remove('hidden');
            document.getElementById('new-file-name').focus();
        }

        showNewFolderModal() {
            dom.newFolderModal.classList.remove('hidden');
            document.getElementById('new-folder-name').focus();
        }

        setupModalHandlers() {
            // New File Modal
            document.getElementById('cancel-new-file').addEventListener('click', () => {
                dom.newFileModal.classList.add('hidden');
            });
            
            document.getElementById('create-new-file').addEventListener('click', () => {
                const name = document.getElementById('new-file-name').value.trim();
                if (name) {
                    this.createNewFile(name);
                    dom.newFileModal.classList.add('hidden');
                    document.getElementById('new-file-name').value = '';
                }
            });
            
            document.getElementById('new-file-name').addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    document.getElementById('create-new-file').click();
                } else if (e.key === 'Escape') {
                    document.getElementById('cancel-new-file').click();
                }
            });

            // New Folder Modal
            document.getElementById('cancel-new-folder').addEventListener('click', () => {
                dom.newFolderModal.classList.add('hidden');
            });
            
            document.getElementById('create-new-folder').addEventListener('click', () => {
                const name = document.getElementById('new-folder-name').value.trim();
                if (name) {
                    this.createNewFolder(name);
                    dom.newFolderModal.classList.add('hidden');
                    document.getElementById('new-folder-name').value = '';
                }
            });
            
            document.getElementById('new-folder-name').addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    document.getElementById('create-new-folder').click();
                } else if (e.key === 'Escape') {
                    document.getElementById('cancel-new-folder').click();
                }
            });

            // Close modals when clicking outside
            [dom.newFileModal, dom.newFolderModal].forEach(modal => {
                modal.addEventListener('click', (e) => {
                    if (e.target === modal) {
                        modal.classList.add('hidden');
                    }
                });
            });

            // Delete confirm modal handlers
            const deleteModal = document.getElementById('delete-confirm-modal');
            const deleteCancelBtn = document.getElementById('delete-cancel-btn');
            const deleteConfirmBtn = document.getElementById('delete-confirm-btn');

            deleteCancelBtn.addEventListener('click', () => {
                deleteModal.classList.add('hidden');
                this._pendingDelete = null;
            });
            deleteConfirmBtn.addEventListener('click', () => {
                if (this._pendingDelete) {
                    this._performDelete(this._pendingDelete);
                    this._pendingDelete = null;
                }
                deleteModal.classList.add('hidden');
            });
            deleteModal.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    deleteCancelBtn.click();
                } else if (e.key === 'Enter') {
                    e.preventDefault();
                    deleteConfirmBtn.click();
                }
            });
            deleteModal.addEventListener('click', (e) => {
                if (e.target === deleteModal) {
                    deleteCancelBtn.click();
                }
            });
        }

        createNewFile(fileName) {
            if (!fileName.toLowerCase().endsWith('.html')) {
                fileName += '.html';
            }
            
            const uniqueName = generateUniqueFileName(fileName.replace('.html', ''), '.html');
            const defaultContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${uniqueName.replace('.html', '')}</title>
</head>
<body>
    <h1>Welcome to ${uniqueName}</h1>
    <p>Start building your HTML page here!</p>
</body>
</html>`;

            state.virtualFiles.set(uniqueName, {
                content: defaultContent,
                isVirtual: true,
                created: Date.now()
            });
            
            state.currentFile = null;
            state.currentFilePath = uniqueName;
            editor.setContent(defaultContent, false); // Don't mark as dirty initially
            this.renderFileTree();
            updateFileStatus(); // Update file status display
            PersistenceManager.save();

            showToast(`Created ${uniqueName}`, 'success');
        }

        createNewFolder(folderName) {
            state.folders.add(folderName);
            this.renderFileTree();
            PersistenceManager.save();
            showToast(`Created folder: ${folderName}`, 'success');
        }

        setupContextMenu() {
            document.addEventListener('click', () => {
                dom.contextMenu.classList.add('hidden');
            });

            dom.contextMenu.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = e.target.closest('[data-action]')?.dataset.action;
                if (action) {
                    this.handleContextAction(action);
                    dom.contextMenu.classList.add('hidden');
                }
            });
        }

        showContextMenu(e, filePath) {
            e.preventDefault();
            e.stopPropagation();
            
            this.contextTarget = filePath;
            dom.contextMenu.style.left = e.pageX + 'px';
            dom.contextMenu.style.top = e.pageY + 'px';
            dom.contextMenu.classList.remove('hidden');
        }

        handleContextAction(action) {
            const filePath = this.contextTarget;
            if (!filePath) return;

            switch (action) {
                case 'open':
                    this.loadFile(filePath);
                    break;
                case 'rename':
                    this.renameFile(filePath);
                    break;
                case 'delete':
                    this.deleteFile(filePath);
                    break;
            }
        }

        renameFile(filePath) {
            const isFolder = [...state.folders].some(f => f === filePath || f.startsWith(filePath + '/'));
            const currentName = filePath.split('/').pop();
            const parentPath = filePath.includes('/') ? filePath.slice(0, filePath.lastIndexOf('/')) : '';

            const newName = prompt('Enter new name:', currentName);
            if (newName === null) {
                // user cancelled
                return;
            }

            const trimmed = (newName || '').trim();
            if (!trimmed) {
                showToast('Name cannot be empty.', 'error');
                return;
            }
            if (trimmed === currentName) {
                return;
            }

            // Basic sanitization and validation
            const invalidChars = /[\\:*?"<>|]/;
            if (invalidChars.test(trimmed)) {
                reportError('Rename failed', new Error('Invalid name. Avoid \\ / : * ? " < > |'), ErrorCodes.INVALID_NAME);
                return;
            }

            const newPath = parentPath ? `${parentPath}/${trimmed}` : trimmed;

            safeAction(() => {
                if (isFolder) {
                    const updatedFolders = new Set();
                    state.folders.forEach(f => {
                        if (f === filePath || f.startsWith(filePath + '/')) {
                            const renamed = f.replace(filePath, newPath);
                            updatedFolders.add(renamed);
                        } else {
                            updatedFolders.add(f);
                        }
                    });
                    state.folders = updatedFolders;

                    const remapMapKeys = (m) => {
                        const entries = Array.from(m.entries());
                        m.clear();
                        entries.forEach(([k, v]) => {
                            if (k.startsWith(filePath + '/')) {
                                const renamedKey = k.replace(filePath + '/', newPath + '/');
                                m.set(renamedKey, v);
                            } else {
                                m.set(k, v);
                            }
                        });
                    };
                    remapMapKeys(state.loadedFiles);
                    remapMapKeys(state.virtualFiles);

                    const newModified = new Set();
                    state.modifiedFiles.forEach(p => {
                        if (p.startsWith(filePath + '/')) {
                            newModified.add(p.replace(filePath + '/', newPath + '/'));
                        } else {
                            newModified.add(p);
                        }
                    });
                    state.modifiedFiles = newModified;

                    if (state.currentFilePath && state.currentFilePath.startsWith(filePath + '/')) {
                        state.currentFilePath = state.currentFilePath.replace(filePath + '/', newPath + '/');
                    }

                    this.renderFileTree();
                    PersistenceManager.save();
                    showToast(`Renamed folder to ${trimmed}`, 'success');
                    return;
                }

                // Rename a single file
                const existsInLoaded = state.loadedFiles.has(newPath);
                const existsInVirtual = state.virtualFiles.has(newPath);
                if (existsInLoaded || existsInVirtual) {
                    throw Object.assign(new Error('A file with that name already exists.'), { code: ErrorCodes.DUPLICATE_PATH });
                }

                if (state.virtualFiles.has(filePath)) {
                    const meta = state.virtualFiles.get(filePath);
                    state.virtualFiles.delete(filePath);
                    state.virtualFiles.set(newPath, meta);
                }
                if (state.loadedFiles.has(filePath)) {
                    const meta = state.loadedFiles.get(filePath);
                    state.loadedFiles.delete(filePath);
                    state.loadedFiles.set(newPath, meta);
                }

                if (state.currentFilePath === filePath) {
                    state.currentFilePath = newPath;
                    updateFileStatus(); // Update file status display after rename
                }

                if (state.modifiedFiles.has(filePath)) {
                    state.modifiedFiles.delete(filePath);
                    state.modifiedFiles.add(newPath);
                }

                this.renderFileTree();
                PersistenceManager.save();
                showToast(`Renamed to ${trimmed}`, 'success');
            }, {
                onError: (err) => reportError('Rename failed', err, err.code || ErrorCodes.UNKNOWN)
            });
        }

        deleteFile(filePath) {
            const name = filePath.split('/').pop();
            const isFolder = [...state.folders].some(f => f === filePath || f.startsWith(filePath + '/'));

            // Populate and show modal
            const deleteModal = document.getElementById('delete-confirm-modal');
            const msgEl = document.getElementById('delete-modal-message');
            const typeLabel = isFolder ? 'folder' : 'file';
            msgEl.textContent = `Are you sure you want to delete the ${typeLabel} "${name}"? This cannot be undone in this session.`;
            deleteModal.classList.remove('hidden');

            // Track pending delete target
            this._pendingDelete = { filePath, isFolder };
        }

        _performDelete(target) {
            const { filePath, isFolder } = target;
            const name = filePath.split('/').pop();

            safeAction(() => {
                if (isFolder) {
                    // Delete folder content from sets/maps
                    const newFolders = new Set();
                    state.folders.forEach(f => {
                        if (!(f === filePath || f.startsWith(filePath + '/'))) {
                            newFolders.add(f);
                        }
                    });
                    state.folders = newFolders;

                    const deleteByPrefix = (m) => {
                        for (const key of Array.from(m.keys())) {
                            if (key === filePath || key.startsWith(filePath + '/')) {
                                m.delete(key);
                            }
                        }
                    };
                    deleteByPrefix(state.virtualFiles);
                    deleteByPrefix(state.loadedFiles);

                    for (const key of Array.from(state.modifiedFiles)) {
                        if (key === filePath || key.startsWith(filePath + '/')) {
                            state.modifiedFiles.delete(key);
                        }
                    }

                    if (state.currentFilePath && (state.currentFilePath === filePath || state.currentFilePath.startsWith(filePath + '/'))) {
                        const remainingFiles = [...state.virtualFiles.keys(), ...state.loadedFiles.keys()];
                        if (remainingFiles.length > 0) {
                            this.loadFile(remainingFiles[0]);
                        } else {
                            this.createNewFile('untitled');
                        }
                    }

                    this.renderFileTree();
                    PersistenceManager.save();
                    showToast(`Deleted folder "${name}"`, 'success');
                    return;
                }

                if (!state.virtualFiles.has(filePath) && !state.loadedFiles.has(filePath)) {
                    throw Object.assign(new Error('Item not found.'), { code: ErrorCodes.NOT_FOUND });
                }

                if (state.virtualFiles.has(filePath)) {
                    state.virtualFiles.delete(filePath);
                }
                if (state.loadedFiles.has(filePath)) {
                    state.loadedFiles.delete(filePath);
                }
                state.modifiedFiles.delete(filePath);

                if (state.currentFilePath === filePath) {
                    const remainingFiles = [...state.virtualFiles.keys(), ...state.loadedFiles.keys()];
                    if (remainingFiles.length > 0) {
                        this.loadFile(remainingFiles[0]);
                    } else {
                        this.createNewFile('untitled');
                    }
                }

                this.renderFileTree();
                PersistenceManager.save();
                showToast(`Deleted "${name}"`, 'success');
            }, {
                onError: (err) => reportError('Delete failed', err, err.code || ErrorCodes.UNKNOWN)
            });
        }

        renderFileTree() {
            dom.fileTree.innerHTML = '';
            
            const hasFiles = state.loadedFiles.size > 0 || state.virtualFiles.size > 0;
            const hasFolders = state.folders.size > 0;
            
            if (!hasFiles && !hasFolders) {
                dom.fileTree.innerHTML = '<p class="text-slate-400 text-center p-4 text-sm">Select a folder to begin or create a new file.</p>';
                return;
            }

            // Build tree structure
            const tree = this.buildTreeStructure();
            
            // Render tree
            this.renderTreeNode(tree, dom.fileTree, 0);
        }

        buildTreeStructure() {
            const tree = { children: {}, files: [] };
            
            // Add folders to tree
            state.folders.forEach(folderPath => {
                this.addPathToTree(tree, folderPath, 'folder');
            });
            
            // Add files to tree
            const allFiles = [
                ...Array.from(state.loadedFiles.keys()).map(path => ({ path, type: 'loaded' })),
                ...Array.from(state.virtualFiles.keys()).map(path => ({ path, type: 'virtual' }))
            ];
            
            allFiles.forEach(({ path, type }) => {
                this.addPathToTree(tree, path, 'file', type);
            });
            
            return tree;
        }

        addPathToTree(tree, path, itemType, fileType = null) {
            const parts = path.split('/').filter(part => part.length > 0);
            let current = tree;
            
            // Navigate/create folder structure
            for (let i = 0; i < parts.length - (itemType === 'file' ? 1 : 0); i++) {
                const part = parts[i];
                if (!current.children[part]) {
                    current.children[part] = { 
                        children: {}, 
                        files: [],
                        name: part,
                        fullPath: parts.slice(0, i + 1).join('/'),
                        type: 'folder'
                    };
                }
                current = current.children[part];
            }
            
            // Add file to appropriate folder
            if (itemType === 'file') {
                const fileName = parts[parts.length - 1];
                current.files.push({
                    name: fileName,
                    fullPath: path,
                    type: 'file',
                    fileType: fileType
                });
            }
        }

        renderTreeNode(node, container, depth) {
            // Sort folders and files
            const sortedFolders = Object.keys(node.children).sort();
            const sortedFiles = node.files.sort((a, b) => a.name.localeCompare(b.name));
            
            // Render folders first
            sortedFolders.forEach(folderName => {
                const folderNode = node.children[folderName];
                const folderElement = this.createTreeFolderElement(folderNode, depth);
                container.appendChild(folderElement);
                
                // Create children container
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'tree-children';
                childrenContainer.id = `folder-${folderNode.fullPath.replace(/[^a-zA-Z0-9]/g, '-')}`;
                
                // Check if folder should be expanded
                const isExpanded = state.folderExpansion.get(folderNode.fullPath) !== false; // Default to expanded
                if (!isExpanded) {
                    childrenContainer.classList.add('collapsed');
                }
                
                container.appendChild(childrenContainer);
                
                // Recursively render children
                this.renderTreeNode(folderNode, childrenContainer, depth + 1);
            });
            
            // Then render files
            sortedFiles.forEach(file => {
                const fileElement = this.createTreeFileElement(file, depth);
                container.appendChild(fileElement);
            });
        }

        createTreeFolderElement(folderNode, depth) {
            const div = document.createElement('div');
            div.className = 'tree-item folder-item';
            div.setAttribute('data-depth', depth);
            
            const isExpanded = state.folderExpansion.get(folderNode.fullPath) !== false;
            
            const hasChildren = Object.keys(folderNode.children).length > 0 || folderNode.files.length > 0;
            
            div.innerHTML = `
                ${hasChildren ? `
                    <div class="folder-toggle ${isExpanded ? 'expanded' : ''}">
                        <svg fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                ` : '<div class="folder-toggle"></div>'}
                <svg class="mr-2 flex-shrink-0" viewBox="0 0 24 24" width="20" height="20" fill="#10b981">
                    <path d="M3 6a2 2 0 0 1 2-2h5l2 2h7a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V6z"></path>
                </svg>
                <span class="truncate font-medium">${folderNode.name}</span>
            `;
            
            // Add click handler for expansion
            if (hasChildren) {
                div.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleFolder(folderNode.fullPath);
                });
            }
            
            return div;
        }

        getFileIcon(ext, fileName) {
            const iconSize = 18;

            switch (ext) {
                case 'html':
                case 'htm':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" fill="#e34c26"/>
                            <path d="M2 17L12 22L22 17" stroke="#e34c26" stroke-width="2" fill="none"/>
                            <path d="M2 12L12 17L22 12" stroke="#e34c26" stroke-width="2" fill="none"/>
                            <text x="12" y="15" text-anchor="middle" font-size="6" font-weight="bold" fill="white">HTML</text>
                        </svg>
                    `;
                case 'css':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" fill="#1572b6"/>
                            <path d="M8 8H16M8 12H16M8 16H13" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                            <text x="12" y="20" text-anchor="middle" font-size="5" font-weight="bold" fill="white">CSS</text>
                        </svg>
                    `;
                case 'js':
                case 'jsx':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" fill="#f7df1e"/>
                            <text x="12" y="15" text-anchor="middle" font-size="8" font-weight="bold" fill="#000">JS</text>
                        </svg>
                    `;
                case 'ts':
                case 'tsx':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" fill="#3178c6"/>
                            <text x="12" y="15" text-anchor="middle" font-size="8" font-weight="bold" fill="white">TS</text>
                        </svg>
                    `;
                case 'json':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" fill="#10b981"/>
                            <path d="M8 8L10 10L8 12M16 8L14 10L16 12M12 6L10 18" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    `;
                case 'md':
                case 'markdown':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" fill="#083fa1"/>
                            <path d="M7 8V16M11 8L9 12L11 16M17 8V12L15 10M17 12V16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    `;
                case 'txt':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" fill="#64748b"/>
                            <path d="M14 2V8H20" fill="none" stroke="white" stroke-width="1.5"/>
                            <path d="M8 12H16M8 16H16" stroke="white" stroke-width="1.5" stroke-linecap="round"/>
                        </svg>
                    `;
                case 'xml':
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" fill="#ff6600"/>
                            <path d="M8 8L12 12L8 16M16 8L12 12L16 16" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    `;
                default:
                    return `
                        <svg class="mr-2 flex-shrink-0" width="${iconSize}" height="${iconSize}" viewBox="0 0 24 24" fill="none">
                            <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2Z" fill="#6b7280"/>
                            <path d="M14 2V8H20" fill="none" stroke="white" stroke-width="1.5"/>
                            <circle cx="12" cy="14" r="2" fill="white"/>
                        </svg>
                    `;
            }
        }

        createTreeFileElement(file, depth) {
            const div = document.createElement('div');
            const isActive = state.currentFilePath === file.fullPath;
            const isModified = state.modifiedFiles.has(file.fullPath);
            
            let className = 'tree-item file-item';
            if (isActive) className += ' active';
            if (isModified) className += ' modified';
            
            div.className = className;
            div.setAttribute('data-depth', depth);
            
            const ext = (file.name.split('.').pop() || '').toLowerCase();
            const icon = this.getFileIcon(ext, file.name);
            
            div.innerHTML = `
                <div class="folder-toggle"></div>
                ${icon}
                <span class="truncate">${file.name}</span>
            `;
            
            div.addEventListener('click', () => this.loadFile(file.fullPath));
            div.addEventListener('contextmenu', (e) => this.showContextMenu(e, file.fullPath));
            
            return div;
        }

        toggleFolder(folderPath) {
            const isExpanded = state.folderExpansion.get(folderPath) !== false;
            state.folderExpansion.set(folderPath, !isExpanded);
            
            // Update UI
            const folderId = `folder-${folderPath.replace(/[^a-zA-Z0-9]/g, '-')}`;
            const childrenContainer = document.getElementById(folderId);
            const folderElement = childrenContainer?.previousElementSibling;
            
            if (childrenContainer && folderElement) {
                const toggle = folderElement.querySelector('.folder-toggle');
                
                if (!isExpanded) {
                    // Expand
                    childrenContainer.classList.remove('collapsed');
                    toggle?.classList.add('expanded');
                } else {
                    // Collapse
                    childrenContainer.classList.add('collapsed');
                    toggle?.classList.remove('expanded');
                }
            }
            
            // Save state
            PersistenceManager.save();
        }



        async loadFile(filePath) {
            if (state.isDirty && !confirm('You have unsaved changes. Continue without saving?')) {
                return;
            }

            return safeAction(async () => {
                if (!filePath) {
                    throw new Error('No file specified.');
                }

                let content = '';

                if (state.virtualFiles.has(filePath)) {
                    content = state.virtualFiles.get(filePath).content ?? '';
                } else if (state.loadedFiles.has(filePath)) {
                    const fileData = state.loadedFiles.get(filePath);
                    if (!fileData) {
                        throw Object.assign(new Error('File metadata missing.'), { code: ErrorCodes.NOT_FOUND });
                    }
                    if (!fileData.isLoaded) {
                        content = await fileData.file.text();
                        fileData.content = content;
                        fileData.isLoaded = true;
                    } else {
                        content = fileData.content ?? '';
                    }
                } else {
                    throw Object.assign(new Error('File not found.'), { code: ErrorCodes.NOT_FOUND });
                }

                state.currentFile = state.loadedFiles.get(filePath)?.file || null;
                state.currentFilePath = filePath;
                editor.setContent(content, state.modifiedFiles.has(filePath));
                this.renderFileTree();
                PersistenceManager.save();

                showToast(`Loaded ${filePath.split('/').pop()}`, 'info');
            }, {
                onError: (err) => reportError('Load file failed', err, err.code || ErrorCodes.READ_FAILED)
            });
        }

        saveCurrentFile() {
            if (!state.currentFilePath) {
                showToast('No file to save', 'error');
                return;
            }

            const content = state.editorContent;
            if (!isBasicHtml(content)) {
                const proceed = confirm('Content may not be valid HTML. Save anyway?');
                if (!proceed) return;
            }

            safeAction(() => {
                const fileName = state.currentFilePath.split('/').pop();
                const blob = new Blob([content], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                // Update the file content in memory
                if (state.virtualFiles.has(state.currentFilePath)) {
                    state.virtualFiles.get(state.currentFilePath).content = content;
                } else if (state.loadedFiles.has(state.currentFilePath)) {
                    state.loadedFiles.get(state.currentFilePath).content = content;
                }

                markDirty(false);
                this.renderFileTree();
                PersistenceManager.save();
                showToast(`Saved ${fileName}`, 'success');
            }, {
                onError: (err) => reportError('Save failed', err)
            });
        }

        async saveAllFiles() {
            return safeAction(async () => {
                if (state.modifiedFiles.size === 0) {
                    throw Object.assign(new Error('No modified files to save.'), { code: ErrorCodes.NO_MODIFIED });
                }

                if (typeof JSZip === 'undefined') {
                    throw Object.assign(new Error('ZIP library not loaded. Please check your internet connection.'), { code: ErrorCodes.ZIP_NOT_LOADED });
                }

                const zip = new JSZip();

                const addToZip = (path, content) => {
                    const normalized = (path || '').replace(/^\/+/, '');
                    zip.file(normalized, content != null ? content : '');
                };

                for (const filePath of state.modifiedFiles) {
                    if (state.virtualFiles.has(filePath)) {
                        const vf = state.virtualFiles.get(filePath);
                        addToZip(filePath, vf?.content ?? '');
                    }
                }

                for (const filePath of state.modifiedFiles) {
                    if (state.loadedFiles.has(filePath)) {
                        const lf = state.loadedFiles.get(filePath);
                        const content = (filePath === state.currentFilePath) ? state.editorContent : (lf?.content ?? '');
                        addToZip(filePath, content);
                    }
                }

                if (!Object.keys(zip.files || {}).length) {
                    throw new Error('No content available to zip.');
                }

                const blob = await zip.generateAsync({ type: 'blob' });
                const a = document.createElement('a');
                a.href = URL.createObjectURL(blob);
                a.download = `project-${Date.now()}.zip`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(a.href);

                state.modifiedFiles.clear();
                state.isDirty = false;
                this.renderFileTree();
                updateFileStatus();
                updateSaveAllButton();
                PersistenceManager.save();

                showToast('Project saved as a zip file!', 'success');
            }, {
                onError: (err) => reportError('Save All failed', err, err.code || ErrorCodes.UNKNOWN)
            });
        }

        createZipFile() {
            // Deprecated placeholder. ZIP creation now handled directly in saveAllFiles() with JSZip.
            return '';
        }
    }

    // ===== SEARCH MANAGER =====
    class SearchManager {
        constructor() {
            this.results = [];
            this.selectedIndex = -1;
            this._bindUI();
        }

        _bindUI() {
            const modal = document.getElementById('search-modal');
            const openBtn = document.getElementById('open-search-btn');
            const cancelBtn = document.getElementById('search-cancel');
            const runBtn = document.getElementById('search-run');
            const repOneBtn = document.getElementById('search-replace-one');
            const repAllBtn = document.getElementById('search-replace-all');

            openBtn.addEventListener('click', () => this.open());
            cancelBtn.addEventListener('click', () => this.close());
            modal.addEventListener('click', (e) => { if (e.target === modal) this.close(); });

            runBtn.addEventListener('click', () => this.search());
            repOneBtn.addEventListener('click', () => this.replaceOne());
            repAllBtn.addEventListener('click', () => this.replaceAll());

            // Keyboard shortcut: Ctrl+F to open, Enter to search inside modal, Esc to close
            window.addEventListener('keydown', (e) => {
                const mod = e.metaKey || e.ctrlKey;
                if (mod && e.key.toLowerCase() === 'f') {
                    e.preventDefault();
                    this.open();
                }
            });

            modal.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    e.preventDefault();
                    this.close();
                } else if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                    e.preventDefault();
                    this.search();
                }
            });
        }

        open() {
            document.getElementById('search-modal').classList.remove('hidden');
            document.getElementById('search-query').focus();
            this.renderResults([]);
        }

        close() {
            document.getElementById('search-modal').classList.add('hidden');
        }

        buildMatcher(query, { regex, matchCase, wholeWord }) {
            if (!regex) {
                // Escape regex special chars if not using regex mode
                const escaped = query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                query = escaped;
            }
            if (wholeWord) {
                query = `\\b${query}\\b`;
            }
            try {
                return new RegExp(query, matchCase ? 'g' : 'gi');
            } catch (e) {
                reportError('Invalid search pattern', e);
                return null;
            }
        }

        collectFiles(currentOnly) {
            if (currentOnly) {
                if (!state.currentFilePath) return [];
                const content = this._getFileContent(state.currentFilePath);
                return [{ path: state.currentFilePath, content }];
            }

            const paths = new Set([
                ...state.virtualFiles.keys(),
                ...state.loadedFiles.keys()
            ]);
            const files = [];
            paths.forEach(p => {
                files.push({ path: p, content: this._getFileContent(p) });
            });
            return files;
        }

        _getFileContent(path) {
            if (path === state.currentFilePath) {
                return state.editorContent ?? '';
            }
            if (state.virtualFiles.has(path)) {
                return state.virtualFiles.get(path)?.content ?? '';
            }
            if (state.loadedFiles.has(path)) {
                const lf = state.loadedFiles.get(path);
                return lf?.content ?? '';
            }
            return '';
        }

        search() {
            const query = document.getElementById('search-query').value || '';
            const opts = {
                regex: document.getElementById('search-regex').checked,
                matchCase: document.getElementById('search-case').checked,
                wholeWord: document.getElementById('search-whole').checked,
                currentOnly: document.getElementById('search-in-open-only').checked
            };

            if (!query) {
                this.renderResults([]);
                showToast('Enter a search query', 'info');
                return;
            }

            const matcher = this.buildMatcher(query, opts);
            if (!matcher) return;

            const files = this.collectFiles(opts.currentOnly);
            const results = [];
            files.forEach(({ path, content }) => {
                if (!content) return;
                let m;
                // reset lastIndex for global regex reuse
                matcher.lastIndex = 0;
                while ((m = matcher.exec(content)) !== null) {
                    const idx = m.index;
                    const lineInfo = this._extractLine(content, idx, m[0].length);
                    results.push({
                        path,
                        index: idx,
                        match: m[0],
                        line: lineInfo.line,
                        lineNo: lineInfo.lineNo,
                        col: lineInfo.col
                    });
                    if (m[0].length === 0) {
                        matcher.lastIndex++;
                    }
                }
            });

            this.results = results;
            this.selectedIndex = results.length ? 0 : -1;
            this.renderResults(results);
            showToast(`${results.length} match(es) found`, 'info');
        }

        _extractLine(content, index, len) {
            const before = content.slice(0, index);
            const lineNo = (before.match(/\n/g) || []).length + 1;
            const lineStart = before.lastIndexOf('\n') + 1;
            const lineEnd = content.indexOf('\n', index);
            const line = content.slice(lineStart, lineEnd === -1 ? content.length : lineEnd);
            const col = index - lineStart + 1;
            return { line, lineNo, col };
        }

        renderResults(results) {
            const container = document.getElementById('search-results');
            container.innerHTML = '';
            if (!results.length) {
                container.innerHTML = '<div class="text-slate-400">No results</div>';
                return;
            }

            results.forEach((r, i) => {
                const item = document.createElement('div');
                item.className = 'p-2 rounded hover:bg-slate-100 dark:hover:bg-slate-700 cursor-pointer flex flex-col gap-0.5';
                item.innerHTML = `
                    <div class="flex items-center gap-2">
                        <span class="text-[10px] px-1 py-0.5 bg-slate-200 dark:bg-slate-700 rounded">${r.path.split('/').pop()}</span>
                        <span class="text-slate-500">L${r.lineNo}:C${r.col}</span>
                    </div>
                    <div class="font-mono text-xs truncate">${this._highlightSnippet(r.line, r.match)}</div>
                `;
                item.addEventListener('click', () => this.jumpToResult(i));
                container.appendChild(item);
            });
        }

        _highlightSnippet(line, match) {
            const safe = (s) => s.replace(/[&<>]/g, c => ({'&':'&','<':'<','>':'>'}[c]));
            const idx = line.toLowerCase().indexOf(match.toLowerCase());
            if (idx === -1) return safe(line);
            return safe(line.slice(0, idx)) + '<span class="bg-yellow-300 text-black px-0.5 rounded">' + safe(line.slice(idx, idx + match.length)) + '</span>' + safe(line.slice(idx + match.length));
        }

        jumpToResult(i) {
            if (i < 0 || i >= this.results.length) return;
            const r = this.results[i];
            fileManager.loadFile(r.path).then(() => {
                // place caret near index
                editor.setSelection(r.index, r.index + r.match.length);
                this.selectedIndex = i;
            });
        }

        replaceOne() {
            if (this.selectedIndex < 0) {
                showToast('Select a result first', 'info');
                return;
            }
            const replacement = document.getElementById('search-replace').value ?? '';
            const r = this.results[this.selectedIndex];
            const content = this._getFileContent(r.path);
            const newContent = content.slice(0, r.index) + replacement + content.slice(r.index + r.match.length);

            this._applyContent(r.path, newContent, true);
            showToast('Replaced one occurrence', 'success');
            // Rerun search to refresh positions
            this.search();
        }

        replaceAll() {
            if (!this.results.length) {
                showToast('No results to replace', 'info');
                return;
            }
            const replacement = document.getElementById('search-replace').value ?? '';
            const query = document.getElementById('search-query').value || '';
            const opts = {
                regex: document.getElementById('search-regex').checked,
                matchCase: document.getElementById('search-case').checked,
                wholeWord: document.getElementById('search-whole').checked,
                currentOnly: document.getElementById('search-in-open-only').checked
            };
            const matcher = this.buildMatcher(query, opts);
            if (!matcher) return;

            const files = this.collectFiles(opts.currentOnly);
            let replaceCount = 0;

            files.forEach(({ path, content }) => {
                if (!content) return;
                matcher.lastIndex = 0;
                const newContent = content.replace(matcher, (m) => { replaceCount++; return replacement; });
                if (newContent !== content) {
                    this._applyContent(path, newContent, true);
                }
            });

            this.search();
            showToast(`Replaced ${replaceCount} occurrence(s)`, 'success');
        }

        _applyContent(path, content, markModified) {
            if (path === state.currentFilePath) {
                editor.setContent(content, markModified);
            } else if (state.virtualFiles.has(path)) {
                const vf = state.virtualFiles.get(path);
                vf.content = content;
                if (markModified) markDirty(true, path);
            } else if (state.loadedFiles.has(path)) {
                const lf = state.loadedFiles.get(path);
                lf.content = content;
                lf.isLoaded = true;
                if (markModified) markDirty(true, path);
            }
            fileManager.renderFileTree();
            PersistenceManager.save();
        }
    }

    // ===== EDITOR CONTROLLER =====
    class EditorController {
        constructor() {
            this._updateSyntaxHighlight = this._updateSyntaxHighlight.bind(this);
            this.debouncedHighlight = debounce(this._updateSyntaxHighlight, 150);
            this.showLineNumbers = localStorage.getItem('htmlPreviewerShowLineNumbers') === 'true';

            // Undo/Redo system
            this.history = [];
            this.historyIndex = -1;
            this.maxHistorySize = 50;
            this.isUndoRedo = false; // Flag to prevent adding to history during undo/redo

            // Autocomplete system
            this.autocompleteVisible = false;
            this.autocompleteIndex = -1;
            this.autocompleteItems = [];
            this.htmlTags = [
                { name: 'div', description: 'Generic container element' },
                { name: 'span', description: 'Inline container element' },
                { name: 'p', description: 'Paragraph element' },
                { name: 'h1', description: 'Main heading' },
                { name: 'h2', description: 'Secondary heading' },
                { name: 'h3', description: 'Tertiary heading' },
                { name: 'h4', description: 'Fourth-level heading' },
                { name: 'h5', description: 'Fifth-level heading' },
                { name: 'h6', description: 'Sixth-level heading' },
                { name: 'a', description: 'Anchor/link element' },
                { name: 'img', description: 'Image element' },
                { name: 'ul', description: 'Unordered list' },
                { name: 'ol', description: 'Ordered list' },
                { name: 'li', description: 'List item' },
                { name: 'table', description: 'Table element' },
                { name: 'tr', description: 'Table row' },
                { name: 'td', description: 'Table cell' },
                { name: 'th', description: 'Table header cell' },
                { name: 'form', description: 'Form element' },
                { name: 'input', description: 'Input field' },
                { name: 'button', description: 'Button element' },
                { name: 'textarea', description: 'Multi-line text input' },
                { name: 'select', description: 'Dropdown selection' },
                { name: 'option', description: 'Option in select' },
                { name: 'nav', description: 'Navigation section' },
                { name: 'header', description: 'Header section' },
                { name: 'footer', description: 'Footer section' },
                { name: 'main', description: 'Main content area' },
                { name: 'section', description: 'Document section' },
                { name: 'article', description: 'Article content' },
                { name: 'aside', description: 'Sidebar content' },
                { name: 'strong', description: 'Strong emphasis' },
                { name: 'em', description: 'Emphasis' },
                { name: 'code', description: 'Inline code' },
                { name: 'pre', description: 'Preformatted text' },
                { name: 'br', description: 'Line break' },
                { name: 'hr', description: 'Horizontal rule' }
            ];
        }

        init() {
            // Debounced function to add to history (to avoid too many history entries)
            const debouncedAddToHistory = debounce(() => {
                if (!this.isUndoRedo) {
                    this.addToHistory(state.editorContent, dom.htmlInput.selectionStart);
                }
            }, 1000);

            dom.htmlInput.addEventListener('input', (e) => {
                state.editorContent = dom.htmlInput.value;

                // Update file content in memory
                if (state.currentFilePath) {
                    if (state.virtualFiles.has(state.currentFilePath)) {
                        state.virtualFiles.get(state.currentFilePath).content = state.editorContent;
                    } else if (state.loadedFiles.has(state.currentFilePath)) {
                        state.loadedFiles.get(state.currentFilePath).content = state.editorContent;
                    }
                }

                markDirty(true);
                this.debouncedHighlight();
                preview.scheduleUpdate(state.editorContent);
                PersistenceManager.save();

                // Add to history (debounced)
                debouncedAddToHistory();

                // Handle autocomplete
                const cursorPos = dom.htmlInput.selectionStart;
                const textBefore = dom.htmlInput.value.substring(0, cursorPos);
                const lastOpenBracket = textBefore.lastIndexOf('<');
                const lastCloseBracket = textBefore.lastIndexOf('>');

                // Show autocomplete if we're typing a tag
                if (lastOpenBracket > lastCloseBracket && lastOpenBracket !== -1) {
                    const tagQuery = textBefore.substring(lastOpenBracket + 1);
                    // Only show if it's a valid tag start (no spaces, special chars)
                    if (/^[a-zA-Z]*$/.test(tagQuery)) {
                        this.showAutocomplete(tagQuery, cursorPos);
                    } else {
                        this.hideAutocomplete();
                    }
                } else {
                    this.hideAutocomplete();
                }
            });

            dom.htmlInput.addEventListener('scroll', () => {
                dom.syntaxHighlight.scrollTop = dom.htmlInput.scrollTop;
                dom.syntaxHighlight.scrollLeft = dom.htmlInput.scrollLeft;

                // Sync line numbers scroll
                const lineNumbersEl = document.getElementById('line-numbers');
                if (lineNumbersEl) {
                    lineNumbersEl.scrollTop = dom.htmlInput.scrollTop;
                }
            });

            // Initialize with default content
            const defaultContent = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to HTML Previewer Pro</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .welcome { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 10px; text-align: center; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 30px; }
        .feature { background: #f8f9fa; padding: 20px; border-radius: 8px; border-left: 4px solid #667eea; }
    </style>
</head>
<body>
    <div class="welcome">
        <h1>🚀 HTML Previewer Pro</h1>
        <p>Your enhanced HTML development environment</p>
    </div>
    
    <div class="features">
        <div class="feature">
            <h3>✨ New Features</h3>
            <ul>
                <li>Persistent project state</li>
                <li>Virtual file system</li>
                <li>Enhanced file management</li>
                <li>Improved collapse panels</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🎯 Quick Actions</h3>
            <ul>
                <li><kbd>Ctrl+N</kbd> - New File</li>
                <li><kbd>Ctrl+S</kbd> - Save File</li>
                <li><kbd>Ctrl+Shift+S</kbd> - Save All</li>
                <li>Right-click files for context menu</li>
            </ul>
        </div>
        
        <div class="feature">
            <h3>🔧 Getting Started</h3>
            <ul>
                <li>Select a project folder</li>
                <li>Create new files/folders</li>
                <li>Import existing files</li>
                <li>Everything auto-saves!</li>
            </ul>
        </div>
    </div>
</body>
</html>`;

            this.setContent(defaultContent, false);

            // Initialize line numbers if enabled
            if (this.showLineNumbers) {
                setTimeout(() => this.toggleLineNumbers(), 100);
            }
        }

        setContent(content, isDirty = true) {
            state.editorContent = content;
            dom.htmlInput.value = content;
            this._updateSyntaxHighlight();
            preview.scheduleUpdate(content);
            markDirty(isDirty);

            // Add to history if not during undo/redo and content is different
            if (!this.isUndoRedo && (this.history.length === 0 || this.history[this.historyIndex]?.content !== content)) {
                this.addToHistory(content, 0);
            }
        }

        setSelection(from, to) {
            const el = dom.htmlInput;
            el.focus();
            el.setSelectionRange(from, to);
            // sync scroll into view roughly
            const before = state.editorContent.slice(0, from);
            const lines = (before.match(/\n/g) || []).length;
            const lineHeight = 21; // approx
            el.scrollTop = Math.max(0, lines * lineHeight - 80);
        }

        toggleLineNumbers() {
            this.showLineNumbers = !this.showLineNumbers;
            localStorage.setItem('htmlPreviewerShowLineNumbers', this.showLineNumbers);

            const container = document.querySelector('.editor-container');
            const lineNumbersEl = document.getElementById('line-numbers');

            if (this.showLineNumbers) {
                container.classList.add('show-line-numbers');
                this._updateLineNumbers();
            } else {
                container.classList.remove('show-line-numbers');
            }

            // Update button appearance
            const button = document.getElementById('line-numbers-toggle');
            if (button) {
                button.classList.toggle('bg-blue-600', this.showLineNumbers);
                button.classList.toggle('text-white', this.showLineNumbers);
            }
        }

        _updateLineNumbers() {
            if (!this.showLineNumbers) return;

            const lineNumbersEl = document.getElementById('line-numbers');
            if (!lineNumbersEl) return;

            const lines = state.editorContent.split('\n');
            const lineNumbers = lines.map((_, index) => (index + 1).toString().padStart(3, ' ')).join('\n');
            lineNumbersEl.textContent = lineNumbers;

            // Sync scroll position
            lineNumbersEl.scrollTop = dom.htmlInput.scrollTop;
        }

        _updateSyntaxHighlight() {
            dom.codeElement.textContent = state.editorContent;
            Prism.highlightElement(dom.codeElement);
            this._updateLineNumbers(); // Update line numbers when content changes
        }

        validateCurrentContent() {
            if (!state.editorContent.trim()) {
                this.showValidationResults({ errors: [], warnings: [] });
                return;
            }

            const results = validateHTML(state.editorContent);
            this.showValidationResults(results);

            const totalIssues = results.errors.length + results.warnings.length;
            if (totalIssues === 0) {
                showToast('HTML validation passed! No issues found.', 'success');
            } else {
                showToast(`Found ${results.errors.length} error(s) and ${results.warnings.length} warning(s)`, 'warning');
            }
        }

        showValidationResults(results) {
            const panel = document.getElementById('validation-panel');
            const resultsContainer = document.getElementById('validation-results');

            if (results.errors.length === 0 && results.warnings.length === 0) {
                panel.classList.add('hidden');
                return;
            }

            panel.classList.remove('hidden');

            let html = '';

            // Show errors
            results.errors.forEach(error => {
                html += `
                    <div class="flex items-start gap-2 p-2 mb-1 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-red-800 dark:text-red-200">
                        <svg class="w-4 h-4 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <div class="font-medium">Error on line ${error.line}</div>
                            <div class="text-sm">${error.message}</div>
                        </div>
                    </div>
                `;
            });

            // Show warnings
            results.warnings.forEach(warning => {
                html += `
                    <div class="flex items-start gap-2 p-2 mb-1 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-yellow-800 dark:text-yellow-200">
                        <svg class="w-4 h-4 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <div>
                            <div class="font-medium">Warning on line ${warning.line}</div>
                            <div class="text-sm">${warning.message}</div>
                        </div>
                    </div>
                `;
            });

            resultsContainer.innerHTML = html;
        }

        addToHistory(content, cursorPosition = null) {
            if (this.isUndoRedo) return; // Don't add to history during undo/redo operations

            // Don't add if content is the same as the last entry
            if (this.history.length > 0 && this.history[this.historyIndex]?.content === content) {
                return;
            }

            // Remove any history after current index (when user makes changes after undo)
            this.history = this.history.slice(0, this.historyIndex + 1);

            // Add new entry
            this.history.push({
                content,
                cursorPosition: cursorPosition || dom.htmlInput.selectionStart,
                timestamp: Date.now()
            });

            // Limit history size
            if (this.history.length > this.maxHistorySize) {
                this.history.shift();
            } else {
                this.historyIndex++;
            }

            this.updateUndoRedoButtons();
        }

        undo() {
            if (this.historyIndex <= 0) return;

            this.isUndoRedo = true;
            this.historyIndex--;

            const entry = this.history[this.historyIndex];
            this.setContent(entry.content, true);

            // Restore cursor position
            setTimeout(() => {
                dom.htmlInput.focus();
                dom.htmlInput.setSelectionRange(entry.cursorPosition, entry.cursorPosition);
                this.isUndoRedo = false;
            }, 0);

            this.updateUndoRedoButtons();
            showToast('Undo', 'info');
        }

        redo() {
            if (this.historyIndex >= this.history.length - 1) return;

            this.isUndoRedo = true;
            this.historyIndex++;

            const entry = this.history[this.historyIndex];
            this.setContent(entry.content, true);

            // Restore cursor position
            setTimeout(() => {
                dom.htmlInput.focus();
                dom.htmlInput.setSelectionRange(entry.cursorPosition, entry.cursorPosition);
                this.isUndoRedo = false;
            }, 0);

            this.updateUndoRedoButtons();
            showToast('Redo', 'info');
        }

        updateUndoRedoButtons() {
            const undoBtn = document.getElementById('undo-btn');
            const redoBtn = document.getElementById('redo-btn');

            if (undoBtn) {
                undoBtn.disabled = this.historyIndex <= 0;
            }

            if (redoBtn) {
                redoBtn.disabled = this.historyIndex >= this.history.length - 1;
            }
        }

        showAutocomplete(query, cursorPosition) {
            if (!query || query.length < 1) {
                this.hideAutocomplete();
                return;
            }

            // Filter tags based on query
            this.autocompleteItems = this.htmlTags.filter(tag =>
                tag.name.toLowerCase().startsWith(query.toLowerCase())
            );

            if (this.autocompleteItems.length === 0) {
                this.hideAutocomplete();
                return;
            }

            const popup = document.getElementById('autocomplete-popup');
            const list = document.getElementById('autocomplete-list');

            // Generate autocomplete items
            list.innerHTML = this.autocompleteItems.map((tag, index) => `
                <div class="autocomplete-item ${index === 0 ? 'selected' : ''}" data-index="${index}">
                    <div class="tag-name">&lt;${tag.name}&gt;</div>
                    <div class="tag-description">${tag.description}</div>
                </div>
            `).join('');

            // Position popup near cursor
            const textarea = dom.htmlInput;
            const textBeforeCursor = textarea.value.substring(0, cursorPosition);
            const lines = textBeforeCursor.split('\n');
            const currentLine = lines.length - 1;
            const currentColumn = lines[lines.length - 1].length;

            // Approximate positioning (this is a simplified approach)
            const lineHeight = 21;
            const charWidth = 8;
            const top = currentLine * lineHeight + 40;
            const left = currentColumn * charWidth + 50;

            popup.style.top = `${Math.min(top, textarea.offsetHeight - 200)}px`;
            popup.style.left = `${Math.min(left, textarea.offsetWidth - 250)}px`;
            popup.classList.remove('hidden');

            this.autocompleteVisible = true;
            this.autocompleteIndex = 0;

            // Add click handlers
            list.querySelectorAll('.autocomplete-item').forEach((item, index) => {
                item.addEventListener('click', () => {
                    this.insertAutocomplete(index);
                });
            });
        }

        hideAutocomplete() {
            const popup = document.getElementById('autocomplete-popup');
            popup.classList.add('hidden');
            this.autocompleteVisible = false;
            this.autocompleteIndex = -1;
        }

        navigateAutocomplete(direction) {
            if (!this.autocompleteVisible || this.autocompleteItems.length === 0) return;

            const list = document.getElementById('autocomplete-list');
            const items = list.querySelectorAll('.autocomplete-item');

            // Remove current selection
            items[this.autocompleteIndex]?.classList.remove('selected');

            // Update index
            if (direction === 'up') {
                this.autocompleteIndex = Math.max(0, this.autocompleteIndex - 1);
            } else {
                this.autocompleteIndex = Math.min(this.autocompleteItems.length - 1, this.autocompleteIndex + 1);
            }

            // Add new selection
            items[this.autocompleteIndex]?.classList.add('selected');
            items[this.autocompleteIndex]?.scrollIntoView({ block: 'nearest' });
        }

        insertAutocomplete(index = this.autocompleteIndex) {
            if (!this.autocompleteVisible || index < 0 || index >= this.autocompleteItems.length) return;

            const tag = this.autocompleteItems[index];
            const textarea = dom.htmlInput;
            const cursorPos = textarea.selectionStart;
            const textBefore = textarea.value.substring(0, cursorPos);
            const textAfter = textarea.value.substring(cursorPos);

            // Find the start of the current tag being typed
            const tagStart = textBefore.lastIndexOf('<');
            if (tagStart === -1) return;

            const beforeTag = textBefore.substring(0, tagStart);
            const newContent = beforeTag + `<${tag.name}>` + textAfter;

            // Update content
            this.setContent(newContent, true);

            // Position cursor after the inserted tag
            const newCursorPos = tagStart + tag.name.length + 2;
            setTimeout(() => {
                textarea.focus();
                textarea.setSelectionRange(newCursorPos, newCursorPos);
            }, 0);

            this.hideAutocomplete();
        }

        extractTitleFromContent() {
            const match = state.editorContent.match(/<title>([\s\S]*?)<\/title>/i);
            return match ? match[1].trim() || 'untitled' : 'untitled';
        }
    }

    // ===== PREVIEW ENGINE =====
    class PreviewEngine {
        constructor() { 
            this._schedule = null; 
            this.setupEventListeners();
        }

        setupEventListeners() {
            document.getElementById('refresh-preview-btn').addEventListener('click', () => {
                this.updatePreview(state.editorContent);
                showToast('Preview refreshed', 'info');
            });

            document.getElementById('toggle-console-btn').addEventListener('click', () => {
                dom.consoleOutputContainer.classList.toggle('hidden');
            });
        }

        scheduleUpdate(content) {
            if (this._schedule) cancelAnimationFrame(this._schedule);
            this._schedule = requestAnimationFrame(() => this.updatePreview(content));
        }

        updatePreview(content) {
            try {
                dom.previewIframe.srcdoc = content || '';
                setTimeout(() => this.captureConsoleLogs(), 50);
            } catch (err) {
                reportError('Preview update failed', err);
            }
        }

        captureConsoleLogs() {
            const win = dom.previewIframe.contentWindow;
            if (!win) return;

            const logToUi = (type, args) => {
                const message = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : arg
                ).join(' ');
                
                const entry = document.createElement('div');
                const color = type === 'error' ? 'text-red-400' : 
                             type === 'warn' ? 'text-yellow-400' : 'text-slate-300';
                entry.className = `${color} py-1`;
                entry.textContent = `[${type.toUpperCase()}] ${message}`;
                
                dom.consoleOutput.appendChild(entry);
                dom.consoleOutput.scrollTop = dom.consoleOutput.scrollHeight;
                
                // Show console if there's an error
                if (type === 'error') {
                    dom.consoleOutputContainer.classList.remove('hidden');
                }
            };

            win.onerror = (msg, src, line, col, err) => {
                logToUi('error', [`${err?.name || 'Error'}: ${msg} (line ${line})`]);
            };

            const oldConsole = win.console;
            win.console = {
                ...oldConsole,
                log: (...args) => { logToUi('log', args); oldConsole.log(...args); },
                error: (...args) => { logToUi('error', args); oldConsole.error(...args); },
                warn: (...args) => { logToUi('warn', args); oldConsole.warn(...args); },
                info: (...args) => { logToUi('info', args); oldConsole.info(...args); },
            };
        }
    }

    // ===== PANEL MANAGER =====
    class PanelManager {
        constructor() {
            this.setupToggleButtons();
            this.restorePanelStates();
            this.setupResizers();
        }

        setupToggleButtons() {
            document.getElementById('files-toggle').addEventListener('click', () => {
                this.togglePane('files', dom.filesPane);
            });

            document.getElementById('code-toggle').addEventListener('click', () => {
                this.togglePane('code', dom.codePane);
            });

            document.getElementById('preview-toggle').addEventListener('click', () => {
                this.togglePane('preview', dom.previewPane);
            });
        }

        togglePane(paneName, paneElement) {
            const isCollapsed = paneElement.classList.toggle('collapsed');
            state.settings.panelStates[paneName] = isCollapsed;
            
            // Update toggle button icon
            const toggle = paneElement.querySelector('.pane-toggle');
            const svg = toggle.querySelector('svg');
            
            if (paneName === 'files' || paneName === 'code') {
                // Left-side panels
                svg.style.transform = isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)';
            } else {
                // Right-side panels  
                svg.style.transform = isCollapsed ? 'rotate(180deg)' : 'rotate(0deg)';
            }
            
            saveSettings();
        }

        restorePanelStates() {
            Object.entries(state.settings.panelStates).forEach(([paneName, isCollapsed]) => {
                if (isCollapsed) {
                    const paneElement = document.getElementById(`${paneName}-pane`);
                    if (paneElement) {
                        paneElement.classList.add('collapsed');
                        
                        const toggle = paneElement.querySelector('.pane-toggle svg');
                        if (toggle) {
                            toggle.style.transform = 'rotate(180deg)';
                        }
                    }
                }
            });

            // Restore persisted sizes
            const sizes = state.settings.panelSizes || {};
            const filesW = parseInt(sizes.filesWidth, 10);
            if (!isNaN(filesW) && filesW > 0) {
                dom.filesPane.style.width = `${filesW}px`;
                dom.filesPane.style.flexBasis = `${filesW}px`;
            }
            const codeW = parseInt(sizes.codeWidth, 10);
            if (!isNaN(codeW) && codeW > 0) {
                dom.codePane.style.flex = '0 0 auto';
                dom.previewPane.style.flex = '1 1 0';
                dom.codePane.style.width = `${codeW}px`;
                dom.codePane.style.flexBasis = `${codeW}px`;
            }
        }

        setupResizers() {
            const filesPane = dom.filesPane;
            const codePane = dom.codePane;
            const previewPane = dom.previewPane;

            const resizerFC = document.getElementById('resizer-files-code');
            const resizerCP = document.getElementById('resizer-code-preview');

            const clamp = (val, min, max) => Math.min(Math.max(val, min), max);

            const makeDrag = (resizerEl, onMove) => {
                if (!resizerEl) return;
                let dragging = false;

                const onMouseMove = (e) => {
                    if (!dragging) return;
                    onMove(e);
                };
                const onMouseUp = () => {
                    if (!dragging) return;
                    dragging = false;
                    document.body.style.cursor = 'default';
                    resizerEl.classList.remove('is-dragging');

                    // Persist sizes on drag end
                    const filesWidth = parseInt(dom.filesPane.style.flexBasis || dom.filesPane.style.width || 300, 10) || 300;
                    const codeWidth = parseInt(dom.codePane.style.flexBasis || dom.codePane.style.width || 0, 10) || null;
                    state.settings.panelSizes = { filesWidth, codeWidth };
                    saveSettings();
                    PersistenceManager.save();
                };

                resizerEl.addEventListener('mousedown', (e) => {
                    e.preventDefault();
                    dragging = true;
                    document.body.style.cursor = 'col-resize';
                    resizerEl.classList.add('is-dragging');
                    window.addEventListener('mousemove', onMouseMove);
                    window.addEventListener('mouseup', onMouseUp, { once: true });
                });

                // Basic keyboard resizing for accessibility
                resizerEl.addEventListener('keydown', (e) => {
                    const step = (e.shiftKey ? 40 : 10);
                    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                        e.preventDefault();
                        const delta = e.key === 'ArrowLeft' ? -step : step;
                        onMove({ clientX: (resizerEl.getBoundingClientRect().left + delta) });
                    }
                });
            };

            // Files <-> Code
            makeDrag(resizerFC, (e) => {
                const containerLeft = dom.app.getBoundingClientRect().left;
                const x = e.clientX - containerLeft;
                const minFiles = 220;
                const maxFiles = Math.max(minFiles, window.innerWidth * 0.5);
                const newFilesW = clamp(x, minFiles, maxFiles);

                filesPane.style.width = `${newFilesW}px`;
                filesPane.style.flexBasis = `${newFilesW}px`;
            });

            // Code <-> Preview
            makeDrag(resizerCP, (e) => {
                const containerLeft = dom.app.getBoundingClientRect().left;
                const x = e.clientX - containerLeft;

                const filesW = filesPane.classList.contains('collapsed') ? 32 : (parseInt(filesPane.style.flexBasis || filesPane.style.width || 300, 10) || 300);
                const total = window.innerWidth - (filesPane.classList.contains('collapsed') ? 32 : filesW);

                // Compute code width relative to container
                const codeLeft = filesPane.classList.contains('collapsed') ? 32 : filesW;
                const newCodeW = clamp(x - codeLeft, 250, total - 250);

                codePane.style.flex = '0 0 auto';
                previewPane.style.flex = '1 1 0';

                codePane.style.width = `${newCodeW}px`;
                codePane.style.flexBasis = `${newCodeW}px`;
            });
        }
    }

    // ===== INITIALIZATION =====
    const fileManager = new FileManager();
    const editor = new EditorController();
    const preview = new PreviewEngine();
    const panelManager = new PanelManager();
    const searchManager = new SearchManager();

    // ===== EVENT LISTENERS =====
    document.getElementById('save-btn').addEventListener('click', () => {
        fileManager.saveCurrentFile();
    });

    dom.saveAllBtn.addEventListener('click', () => {
        fileManager.saveAllFiles();
    });

    document.getElementById('format-btn').addEventListener('click', () => {
        if (state.currentFilePath && state.editorContent) {
            const formatted = formatHTML(state.editorContent);
            editor.setContent(formatted, true);
            showToast('HTML formatted successfully', 'success');
        } else {
            showToast('No content to format', 'info');
        }
    });

    document.getElementById('line-numbers-toggle').addEventListener('click', () => {
        editor.toggleLineNumbers();
        showToast(`Line numbers ${editor.showLineNumbers ? 'enabled' : 'disabled'}`, 'info');
    });

    document.getElementById('validate-btn').addEventListener('click', () => {
        editor.validateCurrentContent();
    });

    document.getElementById('undo-btn').addEventListener('click', () => {
        editor.undo();
    });

    document.getElementById('redo-btn').addEventListener('click', () => {
        editor.redo();
    });

    document.getElementById('dark-mode-toggle').addEventListener('click', () => {
        state.settings.darkMode = !state.settings.darkMode;
        document.documentElement.classList.toggle('dark', state.settings.darkMode);
        saveSettings();
    });

    // Keyboard shortcuts
    window.addEventListener('keydown', (e) => {
        // Handle autocomplete navigation
        if (editor.autocompleteVisible) {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                editor.navigateAutocomplete('down');
                return;
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                editor.navigateAutocomplete('up');
                return;
            } else if (e.key === 'Enter' || e.key === 'Tab') {
                e.preventDefault();
                editor.insertAutocomplete();
                return;
            } else if (e.key === 'Escape') {
                e.preventDefault();
                editor.hideAutocomplete();
                return;
            }
        }

        const mod = e.metaKey || e.ctrlKey;
        
        if (mod && e.key.toLowerCase() === 's') {
            e.preventDefault();
            if (e.shiftKey) {
                fileManager.saveAllFiles();
            } else {
                fileManager.saveCurrentFile();
            }
        } else if (mod && e.key.toLowerCase() === 'n') {
            e.preventDefault();
            fileManager.showNewFileModal();
        } else if (mod && e.key.toLowerCase() === 'f') {
            if (e.shiftKey) {
                // Format shortcut: Ctrl+Shift+F
                e.preventDefault();
                document.getElementById('format-btn').click();
            } else {
                // Search shortcut: Ctrl+F
                e.preventDefault();
                document.getElementById('open-search-btn').click();
            }
        } else if (mod && e.shiftKey && e.key.toLowerCase() === 'h') {
            // Replace all shortcut: Ctrl+Shift+H
            e.preventDefault();
            document.getElementById('search-replace-all')?.click();
        } else if (mod && e.key.toLowerCase() === 'z') {
            // Undo shortcut: Ctrl+Z
            e.preventDefault();
            editor.undo();
        } else if (mod && e.key.toLowerCase() === 'y') {
            // Redo shortcut: Ctrl+Y
            e.preventDefault();
            editor.redo();
        }
    });

    // Drag and drop
    dom.editorContainer.addEventListener('dragover', (e) => {
        e.preventDefault();
        dom.editorContainer.classList.add('drag-over');
    });

    dom.editorContainer.addEventListener('dragleave', () => {
        dom.editorContainer.classList.remove('drag-over');
    });

    dom.editorContainer.addEventListener('drop', async (e) => {
        e.preventDefault();
        dom.editorContainer.classList.remove('drag-over');
        
        const file = e.dataTransfer.files?.[0];
        if (!file || !file.name.toLowerCase().match(/\.(html|htm)$/)) {
            showToast('Only HTML files are supported.', 'error');
            return;
        }

        const content = await file.text();
        const fileName = generateUniqueFileName(file.name.replace(/\.(html|htm)$/, ''), '.html');
        
        state.virtualFiles.set(fileName, {
            content: content,
            isVirtual: true,
            created: Date.now()
        });
        
        state.currentFile = null;
        state.currentFilePath = fileName;
        editor.setContent(content, true);
        fileManager.renderFileTree();
        PersistenceManager.save();
        
        showToast(`Imported ${fileName}`, 'success');
    });

    // Save session before page unload
    window.addEventListener('beforeunload', (e) => {
        try {
            PersistenceManager.save();
        } catch (err) {
            // Avoid blocking unload due to storage errors, just log
            console.warn('Failed to persist on unload', err);
        }
        if (state.modifiedFiles.size > 0) {
            e.preventDefault();
            e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
        }
    });

    // ===== FINAL INITIALIZATION =====
    // Restore settings
    if (state.settings.darkMode) {
        document.documentElement.classList.add('dark');
    }

    // Initialize editor
    editor.init();

    // Try to restore session
    setTimeout(() => {
        PersistenceManager.restore();
        updateSaveAllButton();
    }, 100);

    // Auto-save every 30 seconds
    setInterval(() => {
        PersistenceManager.save();
    }, 30000);
});
